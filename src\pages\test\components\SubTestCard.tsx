import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Headphones, Mic, BookOpen, PenTool } from "lucide-react";

interface SubTest {
  id: string;
  title: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

interface SubTestCardProps {
  subTest: SubTest;
  getTestTypeImage: (type: string) => string;
  formatDate: (dateString: string) => string;
}

const getTypeInfo = (type: string) => {
  switch (type.toLowerCase()) {
    case "listening":
      return {
        label: "<PERSON><PERSON><PERSON>",
        icon: Headphones,
        gradient: "bg-gradient-to-br from-purple-500 to-purple-600",
        iconColor: "text-white",
      };
    case "speaking":
      return {
        label: "Konuşma",
        icon: Mic,
        gradient: "bg-gradient-to-br from-green-500 to-green-600",
        iconColor: "text-white",
      };
    case "reading":
      return {
        label: "Okuma",
        icon: Book<PERSON><PERSON>,
        gradient: "bg-gradient-to-br from-blue-500 to-blue-600",
        iconColor: "text-white",
      };
    case "writing":
    case "academic":
      return {
        label: "<PERSON><PERSON><PERSON>",
        icon: PenTool,
        gradient: "bg-gradient-to-br from-red-500 to-red-600",
        iconColor: "text-white",
      };
    default:
      return {
        label: type,
        icon: BookOpen,
        gradient: "bg-gradient-to-br from-gray-500 to-gray-600",
        iconColor: "text-white",
      };
  }
};

const SubTestCard = ({ subTest, getTestTypeImage }: SubTestCardProps) => {
  const typeInfo = getTypeInfo(subTest.type);

  return (
    <Card
      key={subTest.id}
      className="overflow-hidden hover:shadow-lg transition-shadow duration-300 border-red-100 hover:border-red-200 cursor-pointer"
    >
      <div
        className={`relative h-48 flex items-center justify-center ${typeInfo.gradient}`}
      >
        <typeInfo.icon className={`h-16 w-16 ${typeInfo.iconColor}`} />
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-white/90 text-gray-700">
            <Clock className="h-3 w-3 mr-1" />
            {typeInfo.label}
          </Badge>
        </div>
      </div>

      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <typeInfo.icon className="h-6 w-6 text-red-600" />
          <h3 className="text-xl font-semibold text-gray-900">
            {typeInfo.label}
          </h3>
        </div>

        <Button className="w-full bg-red-600 hover:bg-red-700 text-white">
          Teste Başla
        </Button>
      </CardContent>
    </Card>
  );
};

export default SubTestCard;
