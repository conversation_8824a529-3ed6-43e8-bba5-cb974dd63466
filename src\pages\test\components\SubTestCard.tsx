import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Headphones, Mic, BookOpen, PenTool } from "lucide-react";

interface SubTest {
  id: string;
  title: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

interface SubTestCardProps {
  subTest: SubTest;
  getTestTypeImage: (type: string) => string;
  formatDate: (dateString: string) => string;
}

const getTypeInfo = (type: string) => {
  switch (type.toLowerCase()) {
    case "listening":
      return {
        label: "<PERSON>leme",
        icon: Headphones,
        gradient:
          "bg-gradient-to-br from-purple-900 via-purple-800 to-purple-900",
        altText: "Listening Test - Dinleme Testi",
      };
    case "speaking":
      return {
        label: "Konuşma",
        icon: Mic,
        gradient: "bg-gradient-to-br from-green-900 via-green-800 to-green-900",
        altText: "Speaking Test - Konuşma Testi",
      };
    case "reading":
      return {
        label: "Okuma",
        icon: BookOpen,
        gradient: "bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900",
        altText: "Reading Test - Okuma Testi",
      };
    case "writing":
    case "academic":
      return {
        label: "Yazma",
        icon: PenTool,
        gradient: "bg-gradient-to-br from-red-900 via-red-800 to-red-900",
        altText: "Writing Test - Yazma Testi",
      };
    default:
      return {
        label: type,
        icon: BookOpen,
        gradient: "bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900",
        altText: "Test",
      };
  }
};

const SubTestCard = ({ subTest, getTestTypeImage }: SubTestCardProps) => {
  const typeInfo = getTypeInfo(subTest.type);

  return (
    <Card
      key={subTest.id}
      className="overflow-hidden hover:shadow-lg transition-shadow duration-300 border-red-100 hover:border-red-200 cursor-pointer"
    >
      <div
        className={`relative h-48 ${typeInfo.gradient} flex flex-col justify-between p-6`}
        role="img"
        aria-label={typeInfo.altText}
      >
        {/* Top section with badge */}
        <div className="flex justify-end">
          <Badge variant="secondary" className="bg-white/90 text-gray-700">
            <Clock className="h-3 w-3 mr-1" />
            {typeInfo.label}
          </Badge>
        </div>

        {/* Center section with main content */}
        <div className="flex-1 flex flex-col justify-center items-center text-center">
          <typeInfo.icon className="h-16 w-16 text-white mb-3" />
          <h3 className="text-xl font-bold text-white">{subTest.title}</h3>
        </div>

        {/* Bottom section with decorative pattern */}
        <div className="flex justify-end opacity-20">
          <typeInfo.icon className="h-8 w-8 text-white/20" />
        </div>
      </div>

      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <typeInfo.icon className="h-6 w-6 text-red-600" />
          <h3 className="text-xl font-semibold text-gray-900">
            {typeInfo.label}
          </h3>
        </div>

        <Button className="w-full bg-red-600 hover:bg-red-700 text-white">
          Teste Başla
        </Button>
      </CardContent>
    </Card>
  );
};

export default SubTestCard;
